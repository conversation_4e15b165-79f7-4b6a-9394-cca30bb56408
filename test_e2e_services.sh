#!/bin/bash

set -e

echo "🧪 Testing E2E services startup..."

cd .devcontainer

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose --profile e2e down -v

# Build and start services
echo "🏗️ Building and starting E2E services..."
docker-compose --profile e2e up -d sentry-rails-mini sentry-svelte-mini

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
timeout 120 bash -c '
  until curl -s http://localhost:5000/trace_headers >/dev/null 2>&1; do
    echo "Waiting for Rails service..."
    sleep 2
  done
  echo "✅ Rails service is ready"
  
  until curl -s http://localhost:5001 >/dev/null 2>&1; do
    echo "Waiting for Svelte service..."
    sleep 2
  done
  echo "✅ Svelte service is ready"
'

echo "🎉 All services are running successfully!"

# Show service status
echo "📊 Service status:"
docker-compose --profile e2e ps

# Clean up
echo "🧹 Cleaning up..."
docker-compose --profile e2e down

echo "✅ Test completed successfully!"